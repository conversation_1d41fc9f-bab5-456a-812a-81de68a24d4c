"use client";

import React, { useState, useEffect, useRef } from "react";
import { Download, Clock, MessageCircle, User, Bo<PERSON>, FileText, Mic, Play, Pause, ChevronRight, ChevronLeft, Trash, Video, VideoOff } from "lucide-react";
import jsPDF from "jspdf";
import { useSession } from "next-auth/react";
import VideoPreview from "./VideoPreview";

interface ConversationMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface SelfTakeRecording {
  id: string;
  filename: string;
  url: string;
  timestamp: Date;
  duration?: number;
  rehearsalId: string;
  type: 'audio' | 'video';
  fileSize?: number;
}

interface ResponseTabProps {
  isConnected: boolean;
  conversationMessages: ConversationMessage[];
  onClearMessages: () => void;
  onAddTestMessages?: () => void;
  scriptName?: string | null;
  voiceId?: string | null;
  sessionDuration?: number;
  voiceStatus?: string;
  // Global recording state props
  isRecording: boolean;
  recordingError: string | null;
  recordings: Array<{
    id: string;
    filename: string;
    url: string;
    timestamp: Date;
    rehearsalId: string;
    type: 'audio' | 'video';
    duration?: number;
    fileSize?: number;
  }>;
  playingRecording: string | null;
  recordingMode: 'audio' | 'video';
  recordingDuration: number;
  currentStream: MediaStream | null;
  hasCameraPermission: boolean;
  onStartRecording: () => void;
  onStopRecording: () => void;
  onTogglePlayback: (recording: any) => void;
  onDeleteRecording: (recording: any) => void;
  onRecordingModeChange: (mode: 'audio' | 'video') => void;
  onLoadRecordings: () => void;
  onRequestCameraPermission: () => void;
}

function ResponseTab({
  isConnected,
  conversationMessages,
  onClearMessages,
  onAddTestMessages,
  scriptName,
  voiceId,
  sessionDuration = 0,
  voiceStatus,
  isRecording,
  recordingError,
  recordings,
  playingRecording,
  recordingMode,
  recordingDuration,
  currentStream,
  hasCameraPermission,
  onStartRecording,
  onStopRecording,
  onTogglePlayback,
  onDeleteRecording,
  onRecordingModeChange,
  onLoadRecordings,
  onRequestCameraPermission
}: ResponseTabProps) {
  console.log("[RESPONSE_TAB_PROPS] 🔍 ResponseTab component rendered with props:", {
    conversationMessagesCount: conversationMessages?.length || 0,
    conversationMessages: conversationMessages,
    isConnected,
    voiceStatus,
    scriptName
  });
  const { data: session } = useSession();
  const userId = session?.user?.email || "";

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State for PDF export
  const [isExporting, setIsExporting] = useState(false);

  // State for Self Tape panel
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [isLoadingRecordings, setIsLoadingRecordings] = useState(false);

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [conversationMessages]);

  // Debug: Log conversationMessages to see what's being received
  useEffect(() => {
    console.log("[RESPONSE_TAB_DEBUG] 🔍 ResponseTab received conversationMessages:", {
      messagesCount: conversationMessages.length,
      messages: conversationMessages,
      messagesType: typeof conversationMessages,
      isArray: Array.isArray(conversationMessages)
    });
  }, [conversationMessages]);

  // Load existing recordings when panel opens
  useEffect(() => {
    if (isPanelOpen && userId) {
      setIsLoadingRecordings(true);
      onLoadRecordings();
      setIsLoadingRecordings(false);
    }
  }, [isPanelOpen, userId, onLoadRecordings]);

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const exportToPDF = async () => {
    setIsExporting(true);

    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const lineHeight = 7;
      let yPosition = margin;

      // Header
      pdf.setFontSize(16);
      pdf.setFont("helvetica", "bold");
      pdf.text("Voice Conversation Transcript", margin, yPosition);
      yPosition += lineHeight * 2;

      // Metadata
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      const exportDate = new Date().toLocaleString();
      pdf.text(`Export Date: ${exportDate}`, margin, yPosition);
      yPosition += lineHeight;

      if (scriptName) {
        pdf.text(`Script: ${scriptName}`, margin, yPosition);
        yPosition += lineHeight;
      }

      if (voiceId) {
        pdf.text(`Voice ID: ${voiceId}`, margin, yPosition);
        yPosition += lineHeight;
      }

      pdf.text(`Session Duration: ${formatDuration(sessionDuration)}`, margin, yPosition);
      yPosition += lineHeight;

      pdf.text(`Total Messages: ${conversationMessages.length}`, margin, yPosition);
      yPosition += lineHeight * 2;

      // Messages
      pdf.setFontSize(9);

      for (const message of conversationMessages) {
        // Check if we need a new page
        if (yPosition > pageHeight - margin - 20) {
          pdf.addPage();
          yPosition = margin;
        }

        // Message header
        pdf.setFont("helvetica", "bold");
        const speaker = message.type === "user" ? "User" : "Assistant";
        const timestamp = formatTimestamp(message.timestamp);
        pdf.text(`${speaker} (${timestamp}):`, margin, yPosition);
        yPosition += lineHeight;

        // Message content
        pdf.setFont("helvetica", "normal");
        const lines = pdf.splitTextToSize(message.content, pageWidth - margin * 2);

        for (const line of lines) {
          if (yPosition > pageHeight - margin - 10) {
            pdf.addPage();
            yPosition = margin;
          }
          pdf.text(line, margin + 5, yPosition);
          yPosition += lineHeight;
        }

        yPosition += lineHeight; // Extra space between messages
      }

      // Save the PDF
      const filename = `conversation-transcript-${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(filename);

    } catch (error) {
      console.error("Error exporting PDF:", error);
      alert("Failed to export PDF. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="h-full flex bg-gray-50 dark:bg-gray-900/50 backdrop-blur-sm transition-colors duration-300 courier-font">
      {/* Main Content */}
      <div className={`flex flex-col transition-all duration-300 ${isPanelOpen ? 'w-2/3' : 'w-full'}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-white/10 bg-blue-50 dark:bg-black/20 transition-colors duration-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageCircle className="w-5 h-5 text-blue-600 dark:text-purple-400" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Voice Response Log</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                <span className={`flex items-center space-x-1 ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
                  <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
                  <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatDuration(sessionDuration)}</span>
                </span>
                <span>{conversationMessages.length} messages</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Debug button to test message display */}
            <button
              onClick={() => {
                console.log("[DEBUG] Adding test messages to verify display functionality");
                console.log("[DEBUG] Current conversationMessages:", conversationMessages);
                if (onAddTestMessages) {
                  onAddTestMessages();
                } else {
                  console.log("[DEBUG] onAddTestMessages function not available");
                }
              }}
              className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm"
              title="Debug: Add test messages to verify display"
            >
              <span>Test</span>
            </button>

            <button
              onClick={exportToPDF}
              disabled={conversationMessages.length === 0 || isExporting}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-700 disabled:bg-gray-200 dark:disabled:bg-gray-600 disabled:cursor-not-allowed text-gray-700 dark:text-white rounded-lg transition-colors"
            >
              <Download className="w-4 h-4 ml-2" />
              <span>{isExporting ? 'Exporting...' : ' '}</span>
            </button>

            {onAddTestMessages && (
              <button
                onClick={() => {
                  console.log("[RESPONSE_TAB_TEST] 🧪 Test button clicked in ResponseTab");
                  console.log("[RESPONSE_TAB_TEST] Current conversationMessages in ResponseTab:", conversationMessages);
                  onAddTestMessages();
                  console.log("[RESPONSE_TAB_TEST] onAddTestMessages() called from ResponseTab");
                }}
                className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <MessageCircle className="w-4 h-4" />
                <span>Add Test Messages</span>
              </button>
            )}

            {/* Agent Testing Controls */}
            <button
              onClick={() => {
                console.log("[AGENT_TESTING] 🔄 Comparing agent configurations...");
                if (typeof window !== 'undefined' && (window as any).compareAgentConfigurations) {
                  (window as any).compareAgentConfigurations();
                } else {
                  console.log("[AGENT_TESTING] ❌ compareAgentConfigurations not available");
                }
              }}
              className="flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              <Info className="w-4 h-4" />
              <span>Compare Agents</span>
            </button>

            <button
              onClick={() => {
                console.log("[AGENT_TESTING] 🔄 Toggling to shared agent for testing...");
                if (typeof window !== 'undefined' && (window as any).setUseSharedAgentForTesting) {
                  (window as any).setUseSharedAgentForTesting(true);
                  console.log("[AGENT_TESTING] ✅ Switched to shared agent - refresh conversation to test");
                } else {
                  console.log("[AGENT_TESTING] ❌ setUseSharedAgentForTesting not available");
                }
              }}
              className="flex items-center space-x-2 px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors"
            >
              <User className="w-4 h-4" />
              <span>Test Shared Agent</span>
            </button>
        <button
              onClick={() => setIsPanelOpen(!isPanelOpen)}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors relative group"
              title="Self Tape Recordings"
            >
              <Mic className="w-4 h-4" />
              {isPanelOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}

              {/* Hover tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                Self Tape Recordings
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {conversationMessages.length === 0 ? (
          <div className="text-center text-gray-600 dark:text-gray-400 py-8">
            <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg mb-2">No conversation recorded yet</p>
            <p className="text-sm">Begin a rehearsal to see the transcript here or record a self-take in the panel on the right.</p>
            {/* Debug info */}
            <div className="mt-4 text-xs text-gray-500">
              Debug: Messages count = {conversationMessages.length}
            </div>
          </div>
        ) : (
          <>
            {/* Debug info when messages exist */}
            <div className="text-xs text-gray-500 mb-2">
              Debug: Rendering {conversationMessages.length} messages
            </div>
            {conversationMessages.map((message, index) => {
              console.log(`[RESPONSE_TAB_DEBUG] 🎨 Rendering message ${index}:`, message);
              return (
              <div
                key={message.id}
                className={`flex items-start space-x-3 ${
                  message.type === "user" ? "justify-start" : "justify-start"
                }`}
              >
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  message.type === "user"
                    ? "bg-blue-600"
                    : "bg-gray-500 dark:bg-gray-600"
                }`}>
                  {message.type === "user" ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className={`text-sm font-medium ${
                      message.type === "user" ? "text-blue-600 dark:text-blue-400" : "text-gray-600 dark:text-gray-400"
                    }`}>
                      {message.type === "user" ? "You" : "Assistant"}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-500">
                      {formatTimestamp(message.timestamp)}
                    </span>
                  </div>

                  <div className={`p-3 rounded-lg ${
                    message.type === "user"
                      ? "bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700/30"
                      : "bg-gray-100 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-700/30"
                  } text-gray-900 dark:text-white transition-colors duration-300`}>
                    <p className="text-sm leading-relaxed">{message.content}</p>
                  </div>
                </div>
              </div>
            );
            })}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
      </div>

      {/* Self Tape Panel */}
      {isPanelOpen && (
        <div className="w-1/3 border-l border-gray-200 dark:border-white/10 bg-white dark:bg-black/60 flex flex-col transition-colors duration-300">
          {/* Panel Header */}
          <div className="p-4 border-b border-gray-200 dark:border-white/10 transition-colors duration-300">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mic className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Self Tape Recordings</h3>
              </div>
              <button
                onClick={() => setIsPanelOpen(false)}
                className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>

            {recordingError && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700/30 rounded text-red-600 dark:text-red-400 text-sm transition-colors duration-300">
                {recordingError}
              </div>
            )}
          </div>

          {/* Recording Controls */}
          <div className="p-4 border-b border-gray-200 dark:border-white/10 transition-colors duration-300">
            {/* Recording Mode Toggle */}
            <div className="flex items-center justify-center mb-4">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-1 flex">
                <button
                  onClick={() => onRecordingModeChange('audio')}
                  disabled={isRecording}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    recordingMode === 'audio'
                      ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'
                  } ${isRecording ? 'cursor-not-allowed opacity-50' : ''}`}
                >
                  <Mic className="w-4 h-4 mr-2 inline" />
                  Audio
                </button>
                <button
                  onClick={() => {
                    onRecordingModeChange('video');
                    if (!hasCameraPermission) {
                      onRequestCameraPermission();
                    }
                  }}
                  disabled={isRecording}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    recordingMode === 'video'
                      ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'
                  } ${isRecording ? 'cursor-not-allowed opacity-50' : ''}`}
                >
                  <Video className="w-4 h-4 mr-2 inline" />
                  Video
                </button>
              </div>
            </div>

            {/* Video Preview */}
            {recordingMode === 'video' && (
              <div className="mb-4">
                <VideoPreview
                  stream={currentStream}
                  isRecording={isRecording}
                  recordingDuration={recordingDuration}
                  className="h-48"
                />
              </div>
            )}

            {/* Recording Button */}
            <button
              onClick={isRecording ? onStopRecording : onStartRecording}
              disabled={!userId || (!isRecording && voiceStatus !== "connected") || (recordingMode === 'video' && !hasCameraPermission)}
              className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors ${
                isRecording
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : (!userId || voiceStatus !== "connected" || (recordingMode === 'video' && !hasCameraPermission))
                    ? 'bg-gray-300 dark:bg-gray-600 cursor-not-allowed text-gray-500 dark:text-gray-400'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {recordingMode === 'video' ? (
                <Video className={`w-5 h-5 ${isRecording ? 'animate-pulse' : ''}`} />
              ) : (
                <Mic className={`w-5 h-5 ${isRecording ? 'animate-pulse' : ''}`} />
              )}
              <span>
                {isRecording
                  ? `Stop ${recordingMode === 'video' ? 'Video' : 'Audio'} Recording`
                  : `Start ${recordingMode === 'video' ? 'Video' : 'Audio'} Recording`
                }
              </span>
            </button>

            {/* Status Messages */}
            {!userId ? (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 text-center">
                Please sign in to record
              </p>
            ) : recordingMode === 'video' && !hasCameraPermission ? (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 text-center">
                Camera permission required for video recording
              </p>
            ) : voiceStatus !== "connected" && !isRecording ? (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 text-center">
                Start a rehearsal session in the Connecting tab to enable recording
              </p>
            ) : null}

            {/* Recording Duration */}
            {isRecording && (
              <div className="mt-2 text-center">
                <span className="text-sm text-red-600 dark:text-red-400 font-medium">
                  Recording: {formatDuration(recordingDuration)}
                </span>
              </div>
            )}
          </div>

          {/* Recordings List */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Previous Recordings</h4>
              <span className="text-xs text-gray-500 dark:text-gray-500">
                {recordings.length} recording{recordings.length !== 1 ? 's' : ''}
              </span>
            </div>

            {isLoadingRecordings ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 dark:border-blue-400"></div>
                <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Loading recordings...</span>
              </div>
            ) : recordings.length === 0 ? (
              <div className="text-center py-8">
                <div className="flex items-center justify-center mb-2">
                  <Mic className="w-6 h-6 mr-2 text-gray-400 dark:text-gray-500 opacity-50" />
                  <Video className="w-6 h-6 text-gray-400 dark:text-gray-500 opacity-50" />
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">No recordings yet</p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">Start recording to create your first self-take</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recordings.map((recording) => (
                  <div
                    key={recording.id}
                    className="p-3 bg-blue-50 dark:bg-gray-800/50 rounded-lg border border-blue-200 dark:border-gray-700/30 hover:border-blue-300 dark:hover:border-gray-600/50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          {recording.type === 'video' ? (
                            <Video className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                          ) : (
                            <Mic className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                          )}
                          <span className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase">
                            {recording.type}
                          </span>
                          {recording.duration && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDuration(recording.duration)}
                            </span>
                          )}
                        </div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {recording.filename}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-400">
                          <span>{recording.timestamp.toLocaleString()}</span>
                          {recording.fileSize && (
                            <span>• {formatFileSize(recording.fileSize)}</span>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-2">
                        {recording.type === 'audio' && (
                          <button
                            onClick={() => onTogglePlayback(recording)}
                            className="p-1.5 bg-blue-600 hover:bg-blue-700 rounded text-white transition-colors"
                            title={playingRecording === recording.id ? "Pause" : "Play"}
                          >
                            {playingRecording === recording.id ? (
                              <Pause className="w-3 h-3" />
                            ) : (
                              <Play className="w-3 h-3" />
                            )}
                          </button>
                        )}

                        <button
                          onClick={() => onDeleteRecording(recording)}
                          className="p-1.5 bg-red-600 hover:bg-red-700 rounded text-white transition-colors"
                          title="Delete recording"
                        >
                          <Trash className="w-3 h-3" />
                        </button>
                      </div>
                    </div>

                    {/* Video Player */}
                    {recording.type === 'video' && (
                      <div className="mt-2">
                        <video
                          src={recording.url}
                          controls
                          className="w-full h-32 bg-black rounded object-cover"
                          preload="metadata"
                        />
                      </div>
                    )}

                    {/* Audio Playback Indicator */}
                    {recording.type === 'audio' && playingRecording === recording.id && (
                      <div className="mt-2 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600 dark:bg-blue-400 animate-pulse"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default ResponseTab;
