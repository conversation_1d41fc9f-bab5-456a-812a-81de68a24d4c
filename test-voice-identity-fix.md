# Voice Identity Mapping Fix - Test Plan

## Test Scenario

**Objective**: Verify that selecting Dakota voice (P7x743VjyZEOihNNygQ9) results in the agent introducing itself as "<PERSON>" instead of "<PERSON>".

## Expected Console Log Flow

When you select Dakota voice, you should see this enhanced logging:

### 1. Voice Selection Initiation
```
[VOICE_SELECT] Starting voice selection for: P7x743VjyZEOihNNygQ9
[VOICE_SELECT] 🎤 Updating agent voice to: "Dakota" and updating conversational identity
```

### 2. Current Configuration Analysis
```
[VOICE_SELECT] Current agent config: {
  currentVoiceId: "QQutlXbwqnU9C4Zprxnn",  // <PERSON>'s voice ID (old)
  currentAgentVoiceId: "QQutlXbwqnU9C4Zprxnn",
  targetVoiceId: "P7x743VjyZEOihNNygQ9",   // Dakota's voice ID (new)
  targetVoiceName: "Dakota"
}
```

### 3. Prompt Update Process
```
[VOICE_SELECT] Current prompt length: 2171
[VOICE_SELECT] Replaced "I'm <PERSON>" with "I'm <PERSON>"
[VOICE_SELECT] Prompt update summary: {
  promptChanged: true,
  replacementsMade: 1,
  originalLength: 2171,
  updatedLength: 2174,
  promptPreview: "You are Dakota, a professional line running partner..."
}
```

### 4. API Update Execution
```
[VOICE_SELECT] Patch body: {
  "conversation_config": {
    "tts": { "voice_id": "P7x743VjyZEOihNNygQ9" },
    "agent": {
      "voice_id": "P7x743VjyZEOihNNygQ9",
      "prompt": { "prompt": "Updated prompt with Dakota..." }
    }
  }
}
```

### 5. Verification Results
```
[VOICE_SELECT] 🔍 Verifying voice update...
[VOICE_SELECT] Verification results: {
  expectedVoiceId: "P7x743VjyZEOihNNygQ9",
  finalTtsVoiceId: "P7x743VjyZEOihNNygQ9",
  finalAgentVoiceId: "P7x743VjyZEOihNNygQ9",
  ttsVoiceMatch: true,
  agentVoiceMatch: true,
  promptContainsVoiceName: true,
  updateSuccessful: true
}
```

### 6. Agent Name Extraction (Should Now Show Dakota)
```
[ELEVENLABS] Voice ID found in agent config: "P7x743VjyZEOihNNygQ9"
[ELEVENLABS] Voice mapping lookup: P7x743VjyZEOihNNygQ9 -> Dakota
[ELEVENLABS] 🏷️ Agent name extracted from voice: "Dakota" (voice ID: P7x743VjyZEOihNNygQ9)
[AGENT_MODALITY] ✅ Agent name fetched successfully: "Dakota"
```

### 7. Conversation Test (Agent Should Introduce as Dakota)
```
[AUDIO] AI response received: I'm Dakota, your line running partner for memorization. Let's get your lines locked in.
```

## Test Steps

1. **Open the application** and ensure you're signed in
2. **Select Dakota voice** from the voice carousel
3. **Monitor console logs** for the enhanced debugging information
4. **Start a conversation** and listen to the agent's introduction
5. **Verify the agent says "I'm Dakota"** instead of "I'm Mia"

## Success Criteria

✅ **Voice ID Update**: Both TTS and agent voice IDs should be `P7x743VjyZEOihNNygQ9`
✅ **Prompt Update**: The agent prompt should contain "I'm Dakota" 
✅ **Agent Name Extraction**: Should return "Dakota"
✅ **Conversational Identity**: Agent should introduce itself as "Dakota"
✅ **Verification**: All verification checks should pass

## Troubleshooting

If the fix doesn't work, check for:

1. **API Propagation Delay**: The 2-second wait might not be enough
2. **Prompt Pattern Mismatch**: The existing prompt might use a different pattern than "I'm [Name]"
3. **Configuration Caching**: The agent configuration might be cached
4. **Multiple Agent Instances**: There might be multiple agents with different configurations

## Rollback Plan

If the fix causes issues, the original legacy behavior can be restored by:

1. Reverting the `updateAgentVoiceOnly` function to preserve agent names
2. Removing the prompt update logic
3. Restoring the "DO NOT update agent.name" approach

## Additional Test Cases

After confirming Dakota works, test with:
- **Morgan** (QQutlXbwqnU9C4Zprxnn) → Should say "I'm Morgan"
- **Archie** (kmSVBPu7loj4ayNinwWM) → Should say "I'm Archie"
- **Nathaniel** (AeRdCCKzvd23BpJoofzx) → Should say "I'm Nathaniel"
- **Brad** (vVnXvLYPFjIyE2YrjUBE) → Should say "I'm Brad"
- **Mia** (rCuVrCHOUMY3OwyJBJym) → Should say "I'm Mia"
