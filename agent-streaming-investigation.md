# User Agent Streaming Investigation Guide

## **Issue Hypothesis**
The voice-to-agent identity mapping fix may have broken real-time message streaming for user-specific agents. We need to identify why the onMessage handler isn't triggering or why messages aren't reaching the ResponseTab UI.

## **Investigation Tools Added**

### **1. WebSocket Connection Analysis**
- ✅ Enhanced connection logging for user agent identification
- ✅ Timestamp tracking for connection establishment
- ✅ User-specific agent WebSocket behavior monitoring

### **2. User Agent Configuration Analysis**
- ✅ `window.analyzeUserAgentConfiguration()` - Analyze user agent config
- ✅ Turn management settings analysis
- ✅ TTS configuration verification
- ✅ Client tools configuration check
- ✅ Critical streaming settings identification

### **3. Message Flow Debugging**
- ✅ Enhanced onMessage handler with user agent tracking
- ✅ Message streaming capability analysis
- ✅ Complete message flow from WebSocket to UI
- ✅ React state update verification

## **Test Procedure**

### **Phase 1: Current State Analysis**
1. **Refresh the page** to activate all debugging
2. **Navigate to Response tab**
3. **Check console logs** for:
   ```
   [AGENT_COMPARISON] 🔍 Agent Selection Analysis
   [WEBSOCKET_ANALYSIS] 🔗 WebSocket Connection Established
   ```
4. **Note the agent type**: USER_SPECIFIC vs SHARED vs FALLBACK_SHARED

### **Phase 2: User Agent Configuration Analysis**
1. **Click "Analyze User Agent" button** (purple button)
2. **Review console output** for configuration details:
   - Turn management settings
   - TTS configuration
   - Conversation settings
   - Tools configuration
   - Critical streaming settings
3. **Look for missing or incorrect settings** that might affect streaming

### **Phase 3: Message Streaming Test**
1. **Start a conversation** with user-specific agent
2. **Monitor console logs** for:
   ```
   [MESSAGE_STREAMING] 🔍 onMessage handler triggered!
   [MESSAGE_STREAMING] Agent Info: {conversationAgentId: "agent_01jy..."}
   ```
3. **Check if messages appear** in ResponseTab UI
4. **Try "Add Test Messages"** to verify UI functionality

### **Phase 4: Configuration Verification**
1. **Check if user agent has proper streaming configuration**
2. **Verify client events are properly configured**
3. **Ensure conversation settings allow message streaming**
4. **Confirm WebSocket connection is established correctly**

## **Expected Findings**

### **If User-Specific Agent Configuration is Broken:**
```
❌ User Agent: onMessage doesn't trigger OR messages don't reach UI
🔍 Missing client events configuration
🔍 Incorrect conversation streaming settings
🔍 Missing or wrong client tools configuration
```

### **If Configuration Issues:**
```
🔍 Missing client_events in conversation config
🔍 Incorrect turn management settings
🔍 Wrong TTS streaming configuration
🔍 Missing streaming latency optimization
```

### **If WebSocket Issue:**
```
❌ User Agent: Connection established but no message streaming
🔍 onMessage handler not receiving messages
🔍 WebSocket connection drops or fails
```

## **Key Console Logs to Monitor**

### **Connection Success:**
```
[WEBSOCKET_ANALYSIS] 🔗 WebSocket Connection Established
[USER_AGENT_DEBUG] 🔍 User Agent Analysis: {conversationAgentId: "agent_01jy..."}
```

### **Message Streaming Success:**
```
[MESSAGE_STREAMING] 🔍 onMessage handler triggered!
[MESSAGE_STREAMING] Agent Info: {conversationAgentId: "agent_01jy..."}
[DEBUG_CONVERSATION_STATE] 📊 conversationMessages state changed: {count: 1}
[RESPONSE_TAB_DEBUG] 🔍 ResponseTab received conversationMessages: {messagesCount: 1}
```

### **Configuration Analysis:**
```
[USER_AGENT_ANALYSIS] 🎯 Critical Streaming Settings:
hasClientEvents: true/false
clientEventsArray: [...]
textOnlyMode: true/false
streamingLatency: true/false
```

## **Troubleshooting Steps**

### **If No onMessage Triggers (User Agent):**
1. Check agent configuration for missing client events
2. Verify conversation streaming settings
3. Check if agent has proper WebSocket configuration

### **If onMessage Triggers But No UI Update:**
1. Verify React state updates are working
2. Check if ResponseTab is receiving props correctly
3. Verify component re-rendering

### **If User Agent Configuration is Missing Critical Settings:**
1. Check if user agent creation process is missing streaming config
2. Verify client events configuration in conversation settings
3. Ensure proper TTS streaming configuration

## **Success Criteria**

✅ **Identify Root Cause**: Determine if issue is agent config, WebSocket, or UI
✅ **Configuration Analysis**: Find specific settings causing the issue
✅ **Streaming Verification**: Confirm user agent has proper streaming setup
✅ **Fix Strategy**: Develop plan to fix user agent streaming

## **Next Steps After Investigation**

1. **If Agent Config Issue**: Update user agent creation to include proper streaming settings
2. **If UI Issue**: Fix React state management or component rendering
3. **If WebSocket Issue**: Debug ElevenLabs SDK integration with user agents
4. **If Tools Issue**: Ensure client tools don't interfere with message streaming
5. **If Missing Settings**: Add required client events and streaming configuration

## **Global Test Functions**

Available in browser console:
- `window.analyzeUserAgentConfiguration()` - Analyze user agent config
- `window.testMessageHandler("test message")` - Test message handling
