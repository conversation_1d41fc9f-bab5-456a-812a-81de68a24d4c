# User Agent Streaming Investigation Guide

## **Issue Hypothesis**
The voice-to-agent identity mapping fix may have broken real-time message streaming for user-specific agents. We need to identify why the onMessage handler isn't triggering or why messages aren't reaching the ResponseTab UI.

## **Investigation Tools Added**

### **1. WebSocket Connection Analysis**
- ✅ Enhanced connection logging for user agent identification
- ✅ Timestamp tracking for connection establishment
- ✅ User-specific agent WebSocket behavior monitoring

### **2. User Agent Configuration Analysis**
- ✅ `window.analyzeUserAgentConfiguration()` - Analyze user agent config
- ✅ Turn management settings analysis
- ✅ TTS configuration verification
- ✅ Client tools configuration check
- ✅ Critical streaming settings identification

### **3. Message Flow Debugging**
- ✅ Enhanced onMessage handler with user agent tracking
- ✅ Message streaming capability analysis
- ✅ Complete message flow from WebSocket to UI
- ✅ React state update verification

## **Test Procedure**

### **Phase 1: Current State Analysis**
1. **Refresh the page** to activate all debugging
2. **Navigate to Response tab**
3. **Check console logs** for:
   ```
   [AGENT_COMPARISON] 🔍 Agent Selection Analysis
   [WEBSOCKET_ANALYSIS] 🔗 WebSocket Connection Established
   ```
4. **Note the agent type**: USER_SPECIFIC vs SHARED vs FALLBACK_SHARED

### **Phase 2: Agent Configuration Comparison**
1. **Click "Compare Agents" button** (purple button)
2. **Review console output** for configuration differences:
   - Turn management settings
   - TTS configuration
   - Conversation settings
   - Tools configuration
3. **Look for critical differences** that might affect streaming

### **Phase 3: Message Streaming Test**
1. **Start a conversation** with current agent (likely user-specific)
2. **Monitor console logs** for:
   ```
   [MESSAGE_STREAMING] 🔍 onMessage handler triggered!
   [MESSAGE_STREAMING] Agent Info: {agentType: "USER_SPECIFIC"}
   ```
3. **Check if messages appear** in ResponseTab UI
4. **Try "Add Test Messages"** to verify UI functionality

### **Phase 4: Fallback Agent Test**
1. **Click "Test Shared Agent" button** (orange button)
2. **Refresh the page** to reinitialize conversation with shared agent
3. **Repeat message streaming test**
4. **Compare results** between user-specific and shared agents

## **Expected Findings**

### **If User-Specific Agent is Broken:**
```
✅ Shared Agent: onMessage triggers, messages display in UI
❌ User Agent: onMessage doesn't trigger OR messages don't reach UI
```

### **If Configuration Difference:**
```
🔍 Different turn management settings
🔍 Different client tools configuration
🔍 Different conversation streaming settings
🔍 Missing client events configuration
```

### **If WebSocket Issue:**
```
❌ User Agent: Connection established but no message streaming
✅ Shared Agent: Full WebSocket functionality
```

## **Key Console Logs to Monitor**

### **Connection Success:**
```
[WEBSOCKET_ANALYSIS] 🔗 WebSocket Connection Established
[WEBSOCKET_ANALYSIS] Agent Info: {agentType: "USER_SPECIFIC", activeAgentId: "agent_01jy..."}
```

### **Message Streaming Success:**
```
[MESSAGE_STREAMING] 🔍 onMessage handler triggered!
[MESSAGE_STREAMING] Agent Info: {agentType: "USER_SPECIFIC"}
[DEBUG_CONVERSATION_STATE] 📊 conversationMessages state changed: {count: 1}
[RESPONSE_TAB_DEBUG] 🔍 ResponseTab received conversationMessages: {messagesCount: 1}
```

### **Configuration Differences:**
```
[AGENT_COMPARISON] 💬 Conversation Settings Comparison:
userAgent: {clientEvents: [...]}
sharedAgent: {clientEvents: [...]}
```

## **Troubleshooting Steps**

### **If No onMessage Triggers (User Agent):**
1. Check agent configuration for missing client events
2. Verify conversation streaming settings
3. Check if agent has proper WebSocket configuration

### **If onMessage Triggers But No UI Update:**
1. Verify React state updates are working
2. Check if ResponseTab is receiving props correctly
3. Verify component re-rendering

### **If Shared Agent Works But User Agent Doesn't:**
1. Compare agent configurations in detail
2. Check if user agent creation process is missing streaming config
3. Verify client tools configuration

## **Success Criteria**

✅ **Identify Root Cause**: Determine if issue is agent config or UI
✅ **Configuration Differences**: Find specific settings causing the issue
✅ **Fallback Verification**: Confirm shared agent works correctly
✅ **Fix Strategy**: Develop plan to fix user agent streaming

## **Next Steps After Investigation**

1. **If Agent Config Issue**: Update user agent creation to match shared agent streaming settings
2. **If UI Issue**: Fix React state management or component rendering
3. **If WebSocket Issue**: Debug ElevenLabs SDK integration with user agents
4. **If Tools Issue**: Ensure client tools don't interfere with message streaming

## **Global Test Functions**

Available in browser console:
- `window.compareAgentConfigurations()` - Compare agent configs
- `window.setUseSharedAgentForTesting(true)` - Switch to shared agent
- `window.setUseSharedAgentForTesting(false)` - Switch to user agent
- `window.testMessageHandler("test message")` - Test message handling
